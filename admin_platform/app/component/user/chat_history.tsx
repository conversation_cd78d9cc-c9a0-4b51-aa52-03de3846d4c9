'use client'

import { ChatHistory as ChatHistoryData } from '@/app/type/chat_history'
import { LogStore } from '@/app/type/log_store'
import dayjs from 'dayjs'
import Link from 'next/link'
import { useState, useEffect, Dispatch, SetStateAction, ReactNode } from 'react'
import { FaPen } from 'react-icons/fa6'
import { RiRocket2Fill } from 'react-icons/ri'
import { toast } from 'react-toastify'
import { AnimatePresence, motion } from 'motion/react'
import { DashboardData, DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { TagSelector } from '@/app/component/dashboard/tagSelector'
import { TbMoodLookDown } from 'react-icons/tb'
import { IoMdArrowDropdown } from 'react-icons/io'
import { FaTimes } from 'react-icons/fa'

export function ChatHistory(
  {
    id,
    langsmithProjectId,
    queryChatHistoryByChatId,
    queryLogByChatId,
    createManyDashboardData,
    queryDashboardDataByChatId,
    queryAllDashboardTags,
    findOrCreateDashboardTag
  }:
  {
    id:string,
    queryChatHistoryByChatId(chatId: string): Promise<ChatHistoryData[]>
    queryLogByChatId(chatId: string): Promise<LogStore[]>
    langsmithProjectId: string
    createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void>
    queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]>
    queryAllDashboardTags(): Promise<DashboardTag[]>
    findOrCreateDashboardTag(name: string): Promise<DashboardTag>
  }) {
  const [openForm, setOpenForm] = useState<boolean>(false)
  const [selectedChatHistoryId, setSelectedChatHistoryId] = useState<string[]>([])
  const [chatHistory, setChatHistory] = useState<ChatHistoryData[]>([])
  const [chatLog, setChatLog] = useState<LogStore[]>([])
  const [firstLoad, setFirstLoad] = useState<boolean>(false)
  const [selectedDashboardDataHistoryId, setSelectedDashboardDataHistoryId] = useState<string[]>([])
  useEffect(() => {
    toast.promise(Promise.all([queryChatHistoryByChatId(id), queryLogByChatId(id)]), {
      pending:'query pending',
      success: 'query success',
      error: {
        render:(e) => {
          return `${e.data}`
        }
      }
    }).then(([history, logs]) => {
      setChatHistory(history)
      setChatLog(logs)
    }).finally(() => {
      setFirstLoad(true)
    })
  }, [id])
  useEffect(() => {
    if (window.location.hash) {
      const anchor = document.getElementById(window.location.hash.substring(1))
      if (anchor) {
        anchor.scrollIntoView({ block:'center' })
        anchor.focus()
      }
    } else {
      window.scrollTo(0, document.body.scrollHeight)
    }
  }, [chatHistory, chatLog])
  if (!firstLoad) {
    return <span className="loading loading-dots loading-xl"></span>
  }
  let i = 0
  let j = 0
  const arr = []
  while (i < chatHistory.length || j < chatLog.length) {
    let type: 'chatHistory' | 'chatLog' = 'chatHistory'
    if (j >= chatLog.length) {
      type = 'chatHistory'
    } else if (i >= chatHistory.length) {
      type = 'chatLog'
    } else if (dayjs(chatHistory[i].created_at).isBefore(dayjs(chatLog[j].timestamp))) {
      type = 'chatHistory'
    } else {
      type = 'chatLog'
    }
    if (type == 'chatHistory') {
      const detail = chatHistory[i]
      arr.push(
        <li
          key={detail.id}
        >
          <hr />
          <div className={`timeline-start timeline-box w-full border-0 shadow-none ${selectedDashboardDataHistoryId.includes(detail.id) ? (detail.role == 'user' ? 'border-l-2 rounded-l-none border-info' : ' border-r-2 rounded-r-none border-info') : '' }`}>
            <div
              className={`chat ${detail.role == 'user' ? 'chat-start' : 'chat-end'}`}
            >
              <div className="chat-header items-center">
                {detail.role == 'assistant' && <a className='text-neutral-content hover:text-neutral duration-200 focus:text-secondary' href={`./${detail.chat_id}#${detail.id}`} id={detail.id}>#</a>}
                {detail.is_recalled && <div className='h-4 w-4 bg-error text-center rounded-full text-base-300 tooltip' data-tip="recalled">!</div>}
                {detail.is_send_by_human && <div className='h-4 w-4 bg-info text-center rounded-full text-base-300 tooltip' data-tip="sent by human">人</div>}
                <div>{dayjs(detail.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
                {detail.role == 'user' && <a className='text-neutral-content hover:text-neutral duration-200 focus:text-secondary' href={`./${detail.chat_id}#${detail.id}`} id={detail.id}>#</a>}
                {detail.round_id && <Link target='_blank' href={`https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/${langsmithProjectId}?columnVisibilityModel=%7B%22feedback_stats%22%3Atrue%2C%22reference_example%22%3Afalse%7D&timeModel=%7B%22duration%22%3A%227d%22%7D&searchModel=%7B%22filter%22%3A%22and%28eq%28run_type%2C+%5C%22llm%5C%22%29%2C+and%28eq%28metadata_key%2C+%5C%22round_id%5C%22%29%2C+eq%28metadata_value%2C+%5C%22${detail.round_id}%5C%22%29%29%29%22%7D`} className='btn btn-xs btn-success btn-soft'>lang</Link>}
                {detail.chat_state?.state && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.state).map(([k, v], index) => {
                      return <div key={index}>{k}:{v == true ? 'true' : 'false'}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">state</button>
                </div>}
                {detail.chat_state?.userSlots && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.userSlots).map(([k, v], index) => {
                      return <div key={index}>{k}:{JSON.stringify(v)}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">slots</button>
                </div>}
              </div>
              <div className="chat-bubble max-w-[calc(23dvw)] whitespace-pre-line text-base">
                {detail.content}
              </div>
              <AnimatePresence>
                {openForm &&
              <motion.div key={detail.id} exit={{ scale:0, opacity:0, width:0, height:0 }} initial={{ scale:0, opacity:0, width:0, height:0 }} animate={{ scale:1, opacity:1, width:'auto', height:'auto' }} className='row-2 self-center'><input type='checkbox' className='checkbox' checked={selectedChatHistoryId.includes(detail.id)} onChange={(e) => {
                const checked = e.currentTarget.checked
                setSelectedChatHistoryId((pre) => {
                  if (checked) {
                    pre.push(detail.id)
                    return [...new Set(pre)]
                  } else {
                    return [...pre.filter((item) => item != detail.id)]
                  }
                }) }}/></motion.div>
                }
              </AnimatePresence>
            </div>
          </div>
          <hr />
        </li>
      )
      i++
    } else {
      const detail = chatLog[j]
      arr.push(
        <div
          key={`log${detail.id}`}
          className={'chat chat-start'}
        >
          <div className="chat-header">{dayjs(detail.timestamp).format('YYYY-MM-DD HH:mm:ss')}</div>
          <div className="chat-bubble whitespace-pre-line">
            {detail.msg}
          </div>
        </div>
      )
      j++
    }
  }
  const minimized = []
  let stack = []
  for (const talk of arr) {
    if (talk.key?.includes('log')) {
      stack.push(talk)
    } else {
      if (stack.length > 0) {
        const group = <Minimize arr={[...stack]} key={stack[0].key}/>
        minimized.push(group)
        stack = []
      }
      minimized.push(talk)
    }
  }


  return (
    <div className='flex p-2 gap-2'>
      <ul className="timeline timeline-vertical">
        {...minimized}
      </ul>
      <DashboardDataAddButonAndForm
        setSelectedChatHistoryId={setSelectedChatHistoryId}
        chatId={id}
        createManyDashboardData={createManyDashboardData}
        queryAllDashboardTags={queryAllDashboardTags}
        findOrCreateDashboardTag={findOrCreateDashboardTag}
        openForm={openForm}
        setOpenForm={setOpenForm}
        selectedChatHistoryId={selectedChatHistoryId}
      />
      <DashboardDataButonAndShow setSelectedDashboardDataHistoryId={setSelectedDashboardDataHistoryId} chatId={id} queryDashboardDataByChatId={queryDashboardDataByChatId}/>
      <Rocket/>
    </div>)
}
function Minimize({ arr }:{arr:ReactNode[]}) {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  return <li>
    <hr />
    <div className='timeline-end timeline-box border-0 shadow-none'>
      <button className='btn btn-soft btn-accent h-4 ' onClick={() => {
        setIsOpen((open) => !open)
      }}>
        <motion.div animate={{ rotate:isOpen ? 180 : 0 }}>
          <IoMdArrowDropdown/>
        </motion.div>
      </button>
      <motion.div animate={{ height:isOpen ? 'auto' : 0 }} initial={{ height:0 }} className='overflow-hidden'>
        {...arr}
      </motion.div>

    </div>
    <hr />
  </li>
}

function Rocket() {
  return <button
    className="btn btn-neutral btn-circle fixed right-10 bottom-10"
    onClick={() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }}
  >
    <RiRocket2Fill size={24}/>
  </button>
}

function DashboardDataAddButonAndForm({
  chatId,
  openForm,
  setOpenForm,
  selectedChatHistoryId,
  setSelectedChatHistoryId,
  createManyDashboardData,
  queryAllDashboardTags,
  findOrCreateDashboardTag
}:{
  chatId:string
  openForm: boolean
  setOpenForm: Dispatch<SetStateAction<boolean>>
  selectedChatHistoryId: string[]
  setSelectedChatHistoryId: Dispatch<SetStateAction<string[]>>
  createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void>
  queryAllDashboardTags(): Promise<DashboardTag[]>
  findOrCreateDashboardTag(name: string): Promise<DashboardTag>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [selectedTags, setSelectedTags] = useState<DashboardTag[]>([])
  const [availableTags, setAvailableTags] = useState<DashboardTag[]>([])
  const [showTagSelector, setShowTagSelector] = useState<boolean>(false)

  // 加载可用标签
  useEffect(() => {
    if (openForm && availableTags.length === 0) {
      queryAllDashboardTags().then(setAvailableTags)
    }
  }, [openForm])

  const handleTagSelect = async (tagName: string) => {
    try {
      const tag = await findOrCreateDashboardTag(tagName)
      if (!selectedTags.find((t) => t.id === tag.id)) {
        setSelectedTags([...selectedTags, tag])
      }
      setShowTagSelector(false)
      // 更新可用标签列表
      if (!availableTags.find((t) => t.id === tag.id)) {
        setAvailableTags([...availableTags, tag])
      }
    } catch (error) {
      toast.error('添加标签失败')
    }
  }

  const removeTag = (tagId: string) => {
    setSelectedTags(selectedTags.filter((tag) => tag.id !== tagId))
  }

  return <div>
    {openForm && (
      <div className='fixed right-20 bottom-40 z-50 bg-white border border-gray-300 rounded-2xl shadow-lg p-4 min-w-[400px]'>
        <form onSubmit={(e) => {
          e.preventDefault()
          const formData = new FormData(e.currentTarget)
          const description = formData.get('description') as string
          setLoading(true)
          toast.promise(createManyDashboardData([{
            chat_id: chatId,
            chat_history_id: selectedChatHistoryId,
            tag_ids: selectedTags.map((tag) => tag.id),
            description
          }]), {
            pending: 'create pending',
            success: 'create success',
            error: 'create error'
          }).then(() => {
            setSelectedChatHistoryId([])
            setSelectedTags([])
            setOpenForm(false)
          }).finally(() => {
            setLoading(false)
          })
        }}>
          <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
            <span className="text-lg"> Case collect</span>

            {/* 显示选择的对话数量 */}
            <div className='mb-4'>
              <span className="text-sm text-gray-600">
                已选择 {selectedChatHistoryId.length} 个对话
              </span>
            </div>

            {/* 标签选择 */}
            <label className='label'>标签</label>
            <div className='mb-2'>
              <div className='flex gap-2 items-center mb-2'>
                <button
                  type="button"
                  className='btn btn-primary btn-sm'
                  onClick={() => {
                    console.log('添加标签按钮被点击')
                    setShowTagSelector(!showTagSelector)
                  }}
                >
                  添加标签
                </button>
                {showTagSelector && (
                  <button
                    type="button"
                    className='btn btn-ghost btn-sm'
                    onClick={() => setShowTagSelector(false)}
                  >
                    <FaTimes />
                  </button>
                )}
              </div>

              {showTagSelector && (
                <div className="p-3 border border-gray-300 rounded bg-gray-50 mb-2">
                  <div className="mb-2">
                    <input
                      type="text"
                      placeholder="搜索或输入新标签名称"
                      className="input input-sm w-full"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const tagName = e.currentTarget.value.trim()
                          if (tagName) {
                            handleTagSelect(tagName)
                            e.currentTarget.value = ''
                          }
                        }
                      }}
                    />
                  </div>

                  {/* 显示现有标签 */}
                  {availableTags.length > 0 && (
                    <div>
                      <div className="text-xs text-gray-600 mb-1">现有标签:</div>
                      <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                        {availableTags.slice(0, 10).map((tag) => (
                          <button
                            key={tag.id}
                            type="button"
                            className="px-2 py-1 text-xs rounded border hover:bg-gray-200"
                            style={{ backgroundColor: tag.color || '#E3F2FD' }}
                            onClick={() => handleTagSelect(tag.name)}
                          >
                            {tag.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 已选择的标签 */}
            <div className='flex flex-wrap gap-2 mb-4'>
              {selectedTags.map((tag) => (
                <div
                  key={tag.id}
                  className="flex items-center gap-1 px-2 py-1 rounded-full text-xs border"
                  style={{ backgroundColor: tag.color || '#E3F2FD' }}
                >
                  <span>{tag.name}</span>
                  <button
                    type="button"
                    onClick={() => removeTag(tag.id)}
                    className="ml-1 text-gray-600 hover:text-red-600"
                  >
                    <FaTimes className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
            {/* 描述输入 */}
            <label htmlFor="description" className='label'>描述</label>
            <textarea
              id='description'
              name='description'
              className='textarea focus-within:outline-0 min-h-[100px]'
              placeholder="请输入标注描述..."
            />

            <button
              type="submit"
              className="btn btn-neutral disabled:btn-disabled mt-4 w-full"
              disabled={loading || selectedChatHistoryId.length === 0}
            >
              {loading ? '提交中...' : '提交标注'}
            </button>
          </fieldset>
        </form>
      </div>
    )}
    <button className='fixed right-20 bottom-30 btn btn-neutral btn-circle' onClick={() => {
      setOpenForm((state) => !state)
    }}><FaPen/></button>
  </div>
}

function DashboardDataButonAndShow({
  chatId,
  queryDashboardDataByChatId,
  setSelectedDashboardDataHistoryId
}:{
  chatId:string
  queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]>
  setSelectedDashboardDataHistoryId: Dispatch<SetStateAction<string[]>>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [openForm, setOpenForm] = useState<boolean>(false)
  const [dashboard, setDashboard] = useState<DashboardDataWithChatHistory[]>([])
  const [isInit, setIsInit] = useState<boolean>(false)
  return <div>
    <div className='fixed right-30 bottom-30'>
      <motion.div
        initial={false}
        animate={{
          opacity: openForm ? [null, 1] : [null, 0],
          scaleX: openForm ? [null, 1] : [null, 0],
          scaleY: openForm ? [null, 1] : [null, 0],
          transition: {
            duration: 0.2,
          },
          transformOrigin: 'bottom right',
        }}
        className="top-0 left-0 rounded-md border border-gray-200 bg-base-200 shadow p-2 min-w-[20rem] max-w-[35rem] min-h-[20rem]"
      >
        {loading ? <span className="loading loading-dots loading-xl"></span>
          : <div>
            {dashboard.length == 0 ? <div className="text-gray-500 text-center py-4">暂无标注</div> : <div className='flex flex-col gap-3'>
              <h3 className="text-lg font-semibold text-gray-700">标注记录</h3>
              {dashboard.map((item) => {
                return <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      {/* 显示标签 */}
                      {item.tags && item.tags.length > 0 && (
                        <div className='flex gap-2 flex-wrap mb-2'>
                          {item.tags.map((tag) => (
                            <div
                              key={tag.id}
                              className="px-2 py-1 rounded-full text-xs border"
                              style={{ backgroundColor: tag.color || '#E3F2FD' }}
                            >
                              {tag.name}
                            </div>
                          ))}
                        </div>
                      )}
                      {/* 显示描述 */}
                      <p className='text-sm text-gray-800 whitespace-pre-wrap leading-relaxed'>{item.description}</p>
                      <p className='text-xs text-gray-400 mt-2'>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div>
                    <div className='ml-4'>
                      <Link href={`#${item.chat_history[0]?.id || ''}`}>
                        <button
                          className='btn btn-xs btn-info'
                          onClick={() => {
                            setSelectedDashboardDataHistoryId([...item.chat_history.map((historyItem) => historyItem.id)])
                          }}
                          title="跳转到相关聊天记录"
                        >
                          定位
                        </button>
                      </Link>
                    </div>
                  </div>
                </div>
              })}
            </div>}
          </div>}

      </motion.div>
    </div>
    <button className='fixed right-20 bottom-20 btn btn-neutral btn-circle' onClick={() => {
      setOpenForm((state) => !state)
      if (!loading && !isInit) {
        setLoading(true)
        toast.promise(queryDashboardDataByChatId(chatId), {
          pending:'query pending',
          success:'query success',
          error:'query error'
        }).then((res) => {
          setDashboard(res)
        }).finally(() => {
          setLoading(false)
          setIsInit(true)
        })
      }
    }}><TbMoodLookDown /></button>
  </div>
}
